export default defineAppConfig({
  pages: [
    'pages/splash/index',
    'pages/login/index',
    'pages/home/<USER>',
    'pages/report/index',
    'pages/mine/index',
    'pages/workbench/index',
    'pages/task/list/index',
  ],
  subPackages: [
    {
      root: 'packages/finance/receive/list',
      pages: ['index'],
      name: '收款管理',
      independent: false,
    },
    {
      root: 'packages/finance/receive/detail',
      pages: ['index'],
      name: '收款详情',
      independent: false,
    },
    {
      root: 'packages/finance/receive/add',
      pages: ['index'],
      name: '新增收款',
      independent: false,
    },
    {
      root: 'packages/finance/payment/list',
      pages: ['index'],
      name: '付款管理',
      independent: false,
    },
    {
      root: 'packages/finance/payment/detail',
      pages: ['index'],
      name: '付款详情',
      independent: false,
    },
    {
      root: 'packages/message/list',
      pages: ['index'],
      name: '消息列表',
      independent: false,
    },
    {
      root: 'packages/message/detail',
      pages: ['index'],
      name: '消息详情',
      independent: false,
    },
    {
      root: 'packages/finance/payment/add',
      pages: ['index'],
      name: '新增付款',
      independent: false,
    },
    {
      root: 'packages/stocks/delivery/list',
      pages: ['index'],
      name: '配送任务',
      independent: false,
    },
    {
      root: 'packages/stocks/delivery/detail',
      pages: ['index'],
      name: '配送详情',
      independent: false,
    },
    {
      root: 'packages/stocks/inventory/list',
      pages: ['index'],
      name: '库存管理',
      independent: false,
    },
    {
      root: 'packages/stocks/inventory/detail',
      pages: ['index'],
      name: '库存详情',
      independent: false,
    },
    {
      root: 'packages/stocks/check/list',
      pages: ['index'],
      name: '盘点管理',
      independent: false,
    },
    {
      root: 'packages/stocks/check/detail',
      pages: ['index'],
      name: '盘点详情',
      independent: false,
    },
    {
      root: 'packages/stocks/check/operation',
      pages: ['index'],
      name: '新增盘点',
      independent: false,
    },
    {
      root: 'packages/stocks/check/selected',
      pages: ['index'],
      name: '新增商品列表',
      independent: false,
    },
    {
      root: 'packages/stocks/check/checked',
      pages: ['index'],
      name: '已选商品列表',
      independent: false,
    },
    {
      root: 'packages/stocks/output/list',
      pages: ['index'],
      name: '出库列表',
      independent: false,
    },
    {
      root: 'packages/stocks/output/detail',
      pages: ['index'],
      name: '出库详情',
      independent: false,
    },
    {
      root: 'packages/stocks/output/operation',
      pages: ['index'],
      name: '部分出库',
      independent: false,
    },
    {
      root: 'packages/stocks/input/list',
      pages: ['index'],
      name: '入库列表',
      independent: false,
    },
    {
      root: 'packages/stocks/input/detail',
      pages: ['index'],
      name: '入库详情',
      independent: false,
    },
    {
      root: 'packages/stocks/input/operation',
      pages: ['index'],
      name: '部分入库',
      independent: false,
    },
    {
      root: 'packages/customer/list',
      pages: ['index'],
      name: '客户管理',
      independent: false,
    },
    {
      root: 'packages/customer/add',
      pages: ['index'],
      name: '客户新增',
      independent: false,
    },
    {
      root: 'packages/customer/detail',
      pages: ['index'],
      name: '客户详情',
      independent: false,
    },
    {
      root: 'packages/customer/address/add',
      pages: ['index'],
      name: '新增地址',
      independent: false,
    },
    {
      root: 'packages/customer/contacts/add',
      pages: ['index'],
      name: '新增联系人',
      independent: false,
    },
    {
      root: 'packages/sales/order/list',
      pages: ['index'],
      name: '销售单管理',
      independent: false,
    },
    {
      root: 'packages/sales/order/detail',
      pages: ['index'],
      name: '销售单详情',
      independent: false,
    },
    {
      root: 'packages/sales/order/edit',
      pages: ['index'],
      name: '销售开单',
      independent: false,
    },
    {
      root: 'packages/sales/order/chooseGoodsPage',
      pages: ['index'],
      name: '销售开单选择商品',
      independent: false,
    },
    {
      root: 'packages/sales/order/salesGoodsPage',
      pages: ['index'],
      name: '销售开单已选商品',
      independent: false,
    },
    {
      root: 'packages/sales/order/pricePage',
      pages: ['index'],
      name: '销售开单查看价格信息',
      independent: false,
    },
    {
      root: 'packages/sales/order/purchaseHistoryPage',
      pages: ['index'],
      name: '销售开单采购历史信息',
      independent: false,
    },
    {
      root: 'packages/sales/order/stocksPage',
      pages: ['index'],
      name: '销售开单库存分布信息',
      independent: false,
    },
    {
      root: 'packages/sales/returns/list',
      pages: ['index'],
      name: '销售退货管理',
      independent: false,
    },
    {
      root: 'packages/sales/returns/detail',
      pages: ['index'],
      name: '销售退货详情',
      independent: false,
    },
    {
      root: 'packages/sales/returns/operation',
      pages: ['index'],
      name: '新增销售退货',
      independent: false,
    },
    {
      root: 'packages/sales/returns/returnsItemSelect',
      pages: ['index'],
      independent: false,
    },
    {
      root: 'packages/sales/returns/selectedPage',
      pages: ['index'],
      independent: false,
    },
    {
      root: 'packages/print/setting',
      pages: ['index'],
      name: '打印机设置',
      independent: false,
    },
    {
      root: 'packages/print/preview',
      pages: ['index'],
      name: '打印机预览',
      independent: false,
    },
    {
      root: 'packages/print/goods',
      pages: ['index'],
      name: '打印机商品选择',
      independent: false,
    },
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
    allowsBounceVertical: 'NO',
  },
  animation: false,
});
