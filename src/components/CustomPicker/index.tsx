import { Checklist } from '@nutui/icons-react-taro';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, SafeArea, SearchBar } from '@nutui/nutui-react-taro';
import { ScrollView, View } from '@tarojs/components';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';

export interface ItemProps {
  title: string;
  value: string;
}

interface CustomPickerProps {
  visible: boolean;
  title?: string;
  placeholder?: string;
  onClose: () => void;
  onConfirm: (selectedItems: string[]) => void;
  items?: ItemProps[];
  selected?: string[] | undefined;
  multiple?: boolean;
  needConfirm?: boolean;
}

const CustomPicker: React.FC<CustomPickerProps> = ({
  visible,
  onClose,
  onConfirm,
  title,
  placeholder,
  items,
  selected,
  multiple = true,
  needConfirm = true,
}) => {
  const [selectedItems, setSelectedItems] = useState<string[] | undefined>(selected ?? []);
  const [filteredItems, setFilteredItems] = useState<ItemProps[]>();

  console.log('items', items)

  useEffect(() => {
    if (items) {
      setFilteredItems(items);
    }
  }, [items]);

  useEffect(() => {
    if (selected) {
      setSelectedItems(selected);
    }
  }, [selected]);

  const handleSearch = (value: string) => {
    const term = value;
    if (term) {
      setFilteredItems(items?.filter(({ title }) => title.includes(term)));
    } else {
      setFilteredItems(items);
    }
  };

  const handleConfirm = () => {
    console.log('selectedItems', selectedItems);
    onConfirm(selectedItems!);
    onClose();
  };
  const handleSelect = (item: string) => {
    console.log(item);
    setSelectedItems((prev: any) => {
      if (prev.includes(item)) {
        return prev.filter((i) => i !== item);
      } else {
        if (!multiple) {
          //单选直接替换
          return [item];
        } else {
          return [...prev, item];
        }
      }
    });
    // 不需要确定按钮直接触发选择
    if (!needConfirm) {
      onConfirm([item]);
      onClose();
    }
  };

  return (
    <Popup
      visible={visible}
      onClose={onClose}
      closeable
      left={<span className="relative top-2">{title}</span>}
      position="bottom"
      round
    >
      <View className="pt-[43px] px-[28px] pb-[20px] flex flex-col">
        <SearchBar
          backable={false}
          placeholder={placeholder}
          onChange={handleSearch}
          onClear={() => handleSearch('')}
          style={{
            // @ts-ignore
            '--nutui-searchbar-background': '#FFFFFF',
            '--nutui-searchbar-padding': '0px',
            '--nutui-searchbar-input-height': '40px',
          }}
        />
        <ScrollView scrollY={true} style={{ maxHeight: '50vh' }}>
          {isEmpty(filteredItems) ? (
            <div className="py-[32px] flex justify-center">暂无数据</div>
          ) : (
            filteredItems?.map((item) => (
              <div className="">
                <div
                  key={item?.value}
                  className="flex justify-between items-center py-[32px]"
                  onClick={() => handleSelect(item?.value)}
                >
                  <label>{item?.title}</label>
                  <div>{selectedItems?.includes(item?.value) && <Checklist color="#F49C1F" />}</div>
                </div>
                <Divider />
              </div>
            ))
          )}
        </ScrollView>
        {needConfirm && (
          <div className="my-[17px]">
            <Button className="" size={'large'} type="primary" block onClick={handleConfirm}>
              确定
            </Button>
          </div>
        )}
      </View>
      <SafeArea position="bottom" />
    </Popup>
  );
};

export default CustomPicker;
