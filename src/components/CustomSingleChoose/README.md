# CustomSingleChoose 组件

一个基于 CustomMultipleChoose 的单选组件，专门用于单选场景。

## 功能特性

- 🎯 **单选模式**：专门为单选场景设计
- 🔍 **搜索功能**：支持关键词搜索选项
- 🎨 **布局选择**：支持水平和垂直布局
- 🔄 **重置功能**：可选的重置按钮
- ❌ **取消选择**：可配置是否允许取消已选项

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `onClose` | `() => void` | - | 关闭回调 |
| `onConfirm` | `(selectedItem: any) => void` | - | 确认选择回调 |
| `items` | `ItemsProps[]` | - | 选项数据 |
| `selected` | `any` | `{}` | 已选中的项 |
| `placeholder` | `string` | - | 搜索框占位符 |
| `isSearch` | `boolean` | `false` | 是否显示搜索框 |
| `layout` | `'horizontal' \| 'vertical'` | `'horizontal'` | 布局方式 |
| `showReset` | `boolean` | `true` | 是否显示重置按钮 |
| `allowDeselect` | `boolean` | `true` | 是否允许取消选择 |

## 类型定义

```typescript
interface ItemProps {
  text: string | any;
  value: string | any;
}

interface ItemsProps {
  label: string;
  keyStr: string;
  item?: ItemProps[];
}
```

## 使用示例

### 基础用法

```tsx
import CustomSingleChoose from '@/components/CustomSingleChoose';

const items = [
  {
    keyStr: 'status',
    label: '状态',
    item: [
      { text: '待处理', value: 'pending' },
      { text: '已完成', value: 'completed' },
      { text: '已取消', value: 'cancelled' },
    ],
  },
];

<CustomSingleChoose
  items={items}
  selected={{ status: 'pending' }}
  onConfirm={(selectedMap) => {
    console.log('选中的项:', selectedMap);
  }}
  onClose={() => setVisible(false)}
/>
```

### 带搜索功能

```tsx
<CustomSingleChoose
  items={items}
  selected={selectedValues}
  isSearch={true}
  placeholder="搜索选项..."
  onConfirm={(selectedMap) => {
    setSelectedValues(selectedMap);
    setVisible(false);
  }}
  onClose={() => setVisible(false)}
/>
```

### 垂直布局

```tsx
<CustomSingleChoose
  items={items}
  selected={selectedValues}
  layout="vertical"
  onConfirm={(selectedMap) => {
    setSelectedValues(selectedMap);
  }}
  onClose={() => setVisible(false)}
/>
```

### 不显示重置按钮

```tsx
<CustomSingleChoose
  items={items}
  selected={selectedValues}
  showReset={false}
  onConfirm={(selectedMap) => {
    setSelectedValues(selectedMap);
  }}
  onClose={() => setVisible(false)}
/>
```

### 不允许取消选择

```tsx
<CustomSingleChoose
  items={items}
  selected={selectedValues}
  allowDeselect={false}
  onConfirm={(selectedMap) => {
    setSelectedValues(selectedMap);
  }}
  onClose={() => setVisible(false)}
/>
```

## 与 CustomMultipleChoose 的区别

| 特性 | CustomSingleChoose | CustomMultipleChoose |
|------|-------------------|---------------------|
| 选择模式 | 单选 | 多选 |
| 数据结构 | `{ keyStr: value }` | `{ keyStr: [value1, value2] }` |
| 取消选择 | 可配置 | 支持 |
| 重置按钮 | 可配置显示/隐藏 | 始终显示 |

## 注意事项

1. `items` 必须是数组格式，即使只有一个选项组也要用 `[item]` 包装
2. `selected` 对象的键应该与 `items` 中的 `keyStr` 对应
3. 单选模式下，每个 `keyStr` 对应的值是字符串，而不是数组
4. 当 `allowDeselect` 为 `false` 时，点击已选中项不会取消选择
