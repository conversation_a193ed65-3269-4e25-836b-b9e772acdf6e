
import { CustomerStatus, DeliveryAmountType, SettleType } from "@/packages/customer/list/types/CustomerSaveEntity";

export const CustomerStatusEnum = {
    [CustomerStatus.ENABLE]: { text: '启用', status: 'success', color: 'green' },
    [CustomerStatus.DISABLE]: { text: '禁用', status: 'danger', color: 'red' },
    [CustomerStatus.PENDING]: { text: '待审核', status: 'warning', color: 'orange' },
};

export const CustomerStatusOptions = [
    { title: '启用', value: CustomerStatus.ENABLE },
    { title: '禁用', value: CustomerStatus.DISABLE },
    { title: '待审核', value: CustomerStatus.PENDING },
]

export const DeliveryAmountTypeEnum = {
    [DeliveryAmountType.Free]: { text: '免运费' },
    [DeliveryAmountType.Fixed]: { text: '固定运费' },
    [DeliveryAmountType.Bargaining]: { text: '议价运费' },
};

export const DeliveryAmountTypeOptions = [
    { title: '免运费', value: DeliveryAmountType.Free },
    { title: '固定运费', value: DeliveryAmountType.Fixed },
    { title: '议价运费', value: DeliveryAmountType.Bargaining },
]

export const SettleTypeOptions = [
    { title: 'COD', value: SettleType.COD },
    { title: 'Weekly', value: SettleType.WEEKLY },
    { title: 'Monthly', value: SettleType.MONTHLY },
]