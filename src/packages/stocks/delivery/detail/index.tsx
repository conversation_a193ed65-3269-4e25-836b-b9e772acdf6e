import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import DetailList from '@/components/DetailList';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { TimeFormat } from '@/components/TimeFormat';
import useUserStore from '@/stores/user';
import { Copy, Phone } from '@nutui/icons-react-taro';
import { Button, Image, ImagePreview, SafeArea, Step, Steps, Tag } from '@nutui/nutui-react-taro';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { batchAssign, finishDelivery, queryDeliveryDetail, startDelivery } from '../services';
import { DeliveryDetailEntity } from '../types/delivery.entity';
import { DeliveryState, DeliveryStateMap } from '../types/delivery.enums';

// 复制文本
const handleCopy = (text: string) => {
  Taro.setClipboardData({
    data: text,
    success: () => {
      Taro.showToast({
        title: '复制成功',
        icon: 'success',
      });
    },
  });
};

// 拨打电话
const handleCall = (phone: string) => {
  Taro.makePhoneCall({
    phoneNumber: phone,
  });
};

const buttonProps = {
  size: 'large',
  type: 'primary',
  block: true,
  className: 'h-[88px] text-[32px] rounded-[12px]',
};

export default function DeliveryDetail() {
  const router = useRouter();
  const { id } = router.params;
  const [detail, setDetail] = useState<DeliveryDetailEntity>();
  const [loading, setLoading] = useState(false);
  const [init, setInit] = useState(0)
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const userStore = useUserStore();
  const currentUser = userStore.userInfo;

  console.log(currentUser)

  // 获取详情数据
  const fetchDetail = async () => {
    if (!id) return;
    try {
      const result = await queryDeliveryDetail({ id });
      setDetail({
        ...result,
        images: result.images?.split(',').map(item => {
          return {
            src: item,
          }
        }),
      });
    } catch (error) {
      console.error('获取配送详情失败:', error);
      Taro.showToast({ title: '获取详情失败', icon: 'none' });
    }
  };

  useEffect(() => {
    fetchDetail();
  }, [id]);

  const pickUpTask = async () => {
    if (!detail?.id) return;
    const result = await batchAssign({ deliveryManId: currentUser.id, deliveryMan: currentUser.name, idList: [detail.id] });
    if (result) {
      Taro.showToast({ title: '领取任务成功', icon: 'success' });
      await fetchDetail();
    }
  };

  const startTask = async () => {
    if (!detail?.id) return;
    const result = await startDelivery({ id: detail.id });
    if (result) {
      Taro.showToast({ title: '开始任务成功', icon: 'success' });
      await fetchDetail();
    }
  };

  const 

  // 处理配送操作
  const handleDeliveryAction = async () => {
    if (!detail?.id) return;

    setLoading(true);
    try {
      let result = false;
      let successMessage = '';

      switch (detail.state) {
        case DeliveryState.PENDING:
          // 待领取 -> 领取任务
          result = await batchAssign({ deliveryManId: currentUser.id, deliveryMan: currentUser.name, idList: [detail.id] });
          successMessage = '任务领取成功';
          break;
        case DeliveryState.PICKED_UP:
          // 已领取 -> 开始任务
          result = await startDelivery({ id: detail.id });
          successMessage = '任务开始成功';
          break;
        case DeliveryState.IN_DELIVERY:
          // 配送中 -> 确认完成
          result = await finishDelivery({ id: detail.id });
          successMessage = '配送完成';
          break;
        default:
          Taro.showToast({ title: '当前状态无法操作', icon: 'none' });
          return;
      }

      if (result) {
        Taro.showToast({ title: successMessage, icon: 'success' });
        // 刷新详情
        await fetchDetail();
      }
    } catch (error) {
      console.error('操作失败:', error);
      Taro.showToast({ title: '操作失败', icon: 'none' });
    } finally {
      setLoading(false);
    }
  };



  if (!detail) {
    return (
      <div className="flex flex-col h-screen">
        <CustomNavBar title="配送详情" />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-[28px] text-[#999]">加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar title="配送详情" />

      <div className="flex-1 overflow-auto py-[50px]">
        <div className='px-[28px]'>
          <div className="text-[32px] font-medium mb-[8px] leading-[1.5]">
            {detail.deliveryTargetName}
          </div>
          <div className="flex align-center align-center">
            <div className="text-[28px] text-[#00000073] mr-2">
              {detail.bizBillNo}
            </div>
            <div className="flex gap-[8px] flex-wrap">
              {detail.isUrgent === 1 && (
                <Tag type="danger" color='#D1382BFF'>紧急</Tag>
              )}
              <Tag type="primary">
                {detail.billTypeDesc}
              </Tag>
              <Tag type={DeliveryStateMap[detail.state!]?.status} color={
                DeliveryStateMap[detail.state!]?.color
              }>
                {detail.stateDesc}
              </Tag>
            </div>
          </div>
        </div>


        {/* 基本信息卡片 */}
        <Card>
          {/* 详细信息 */}
          <DetailList
            className="text-[28px]"
            colon
            labelColor="text-[#00000099]"
            dataSource={[
              {
                label: '业务单号',
                value: (
                  <span className='flex items-center gap-[8px]'>
                    {detail.origBillNo || '-'}
                    <Copy color='#000000CC' className='ml-1' onClick={() => handleCopy(detail.origBillNo || '')} />
                  </span>
                ),
              },
              {
                label: '仓库',
                value: detail.warehouseName || '-',
              },
              {
                label: '配送员',
                value: detail.deliveryMan || '-',
              },
              {
                label: '联系人',
                value: (
                  <span className='flex items-center gap-[8px]'>
                    {detail.contactName || '-'} {detail.contactPhone || '-'}
                    {detail.contactPhone && (
                      <Phone color='#000000CC' className='ml-1' onClick={() => handleCall(detail.contactPhone || '')} />
                    )}
                  </span>
                ),
              },
              {
                label: '地址',
                value: detail.deliveryAddress || '-',
              },
              {
                label: '创建时间',
                value: <TimeFormat time={detail.createTime} showTime />,
              },
              {
                label: '期望到达',
                value: <TimeFormat time={detail.expectedArrTime} showTime />,
              },
              {
                label: '备注',
                value: detail.remark || '-',
              },
              {
                label: '配送图片',
                value: <div className='flex gap-[8px]'>
                  {detail.images?.map((item, index) => {
                    return <Image
                      src={item.src}
                      width={40}
                      height={40}
                      className="rounded-[8px]"
                      onClick={() => {
                        setInit(index);
                        setShowPreview(true);
                      }}
                    />
                  })}
                </div>
                ,
              },
            ]}
          />
        </Card>

        {/* 任务记录 */}
        {[
          DeliveryState.PICKED_UP,
          DeliveryState.IN_DELIVERY,
          DeliveryState.COMPLETED,
        ].includes(detail.state) && <Card title="任务记录">
            <Steps dot direction="vertical" style={{
              '--nutui-steps-base-description-font-size': '14px',
              '--nutui-steps-base-description-color': '#999',
            }}>
              <Step value={1} title="领取时间" description={<TimeFormat time={detail.createTime} showTime />} />
              {detail.beginTime && <Step value={2} title="开始时间" description={<TimeFormat time={detail.beginTime} showTime />} />}
              {detail.finishTime && <Step value={3} title="完成时间" description={<TimeFormat time={detail.finishTime} showTime />} />}
            </Steps>
          </Card>}

        {/* 商品明细 */}
        {Boolean(detail.detailList?.length) && (
          <Card title="商品明细">
            <div className="space-y-[24px]">
              {detail.detailList?.map((item, index) => (
                <div key={item.id || index} className="border-b border-[#f0f0f0] pb-[24px] last:border-b-0 last:pb-0">
                  <div className="flex items-start gap-[24px]">
                    {/* 商品图片 */}
                    <div className="w-[80px] h-[80px] bg-[#f5f5f5] rounded-[8px] flex items-center justify-center">
                      <Image src={item.images[0]} width={80} />
                    </div>
                    <div className="flex-1">
                      <div className="text-[28px] font-medium mb-[8px]">
                        {item.itemName || '-'}
                      </div>
                      <div className='flex text-[28px] text-[#999999] mt-1'>
                        <ItemsWithDivider
                          items={[
                            item.itemSn,
                            item.brandName,
                            item.categoryName,
                            item.unitName,
                          ]}
                        />
                      </div>
                      <div className="mt-2">
                        x{item.amount || 0}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* 汇总信息 */}
              <div className="p-[24px] flex justify-between text-[28px]">
                <span>商品合计: {detail.detailList?.length}</span>
                <span>数量合计: {detail.detailList?.reduce((sum, item) => sum + (item.amount || 0), 0)}</span>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* 底部固定按钮 */}
      {[DeliveryState.PENDING, DeliveryState.PICKED_UP, DeliveryState.IN_DELIVERY].includes(detail.state) && (
        <div className="fixed left-0 right-0 bottom-0 bg-white px-[28px] py-[24px] border-t border-[#f0f0f0]">
          {
            detail.state == DeliveryState.PENDING &&
            <Button
              {...buttonProps}
              loading={loading}
              onClick={pickUpTask}
            >
              领取任务
            </Button>
          }
          {
            detail.state == DeliveryState.PICKED_UP && currentUser?.id === detail.deliveryManId &&
            <Button
              {...buttonProps}
              loading={loading}
              onClick={startTask}
            >
              核对完毕，开始任务
            </Button>
          }
          {
            detail.state == DeliveryState.IN_DELIVERY && currentUser?.id === detail.deliveryManId &&
            <Button
              {...buttonProps}
              loading={loading}
              onClick={comfirmComplete}
            >
              确认完成
            </Button>
          }
        </div>
      )}

      <ImagePreview
        autoPlay
        images={detail.images}
        visible={showPreview}
        onClose={() => setShowPreview(false)}
        defaultValue={init}
      />

      <SafeArea position="bottom" />
    </div>
  );
}