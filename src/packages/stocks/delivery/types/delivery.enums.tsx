
export enum DeliveryState {
  /** 待领取 */
  PENDING = 1,
  /** 已领取 */
  PICKED_UP = 2,
  /** 配送中 */
  IN_DELIVERY = 3,
  /** 配送完成 */
  COMPLETED = 4,
  /** 已取消 */
  CANCELLED = 5,
}

export const DeliveryStateMap = {
  [DeliveryState.PENDING]: {
    text: '待领取',
    status: 'Default',
  },
  [DeliveryState.PICKED_UP]: {
    text: '已领取',
    status: 'Processing',
  },
  [DeliveryState.IN_DELIVERY]: {
    text: '配送中',
    status: 'Processing',
  },
  [DeliveryState.COMPLETED]: {
    text: '配送完成',
    status: 'Success',
  },
  [DeliveryState.CANCELLED]: {
    text: '已取消',
    status: 'Error',
  },
}

/**
 * Delivery type enum
 */
export enum DeliveryType {
  /** 送货 */
  DELIVERY = 1,
  /** 取货 */
  PICKUP = 2,
}

export const DeliveryTypeMap = {
  [DeliveryType.DELIVERY]: {
    text: '送货',
  },
  [DeliveryType.PICKUP]: {
    text: '取货',
  },
}

/**
 * Distribution mode enum
 */
export enum DistributionMode {
  /** 客户自提 */
  SELF_PICKUP = 1,
  /** 商家配送 */
  MERCHANT_DELIVERY = 2,
  /** 快递物流 */
  LOGISTICS = 3,
}

export const DistributionModeMap = {
  [DistributionMode.SELF_PICKUP]: {
    text: '客户自提',
  },
  [DistributionMode.MERCHANT_DELIVERY]: {
    text: '商家配送',
  },
  [DistributionMode.LOGISTICS]: {
    text: '快递物流',
  },
}


export enum TargetType {
  /**
   * 供应商
   */
  SUPPLIER = 1,
  /**
   * 客户
   */
  CUSTOMER = 2,
}
